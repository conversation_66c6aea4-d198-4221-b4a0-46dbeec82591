repos:
    # hooks for checking files
    -   repo: https://github.com/pre-commit/pre-commit-hooks
        rev: v4.6.0
        hooks:
            -   id: trailing-whitespace
            -   id: end-of-file-fixer
            -   id: check-yaml

    # hooks for linting code
    -   repo: https://github.com/psf/black
        rev: 24.8.0
        hooks:
            -   id: black
                args: [
                    --line-length=120, # refer to pyproject.toml
                ]

    -   repo: https://github.com/PyCQA/flake8
        rev: 7.1.1
        hooks:
            -   id: flake8
                args: [
                    --max-line-length=120, # refer to pyproject.toml
                    --extend-ignore=E203,E231
                ]
