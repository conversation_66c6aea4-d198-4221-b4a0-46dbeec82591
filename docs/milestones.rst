Citation and Milestones
=======================

Citing PyPOTS
^^^^^^^^^^^^^
**[Updates in Jun 2023]** 🎉A short version of the PyPOTS paper is accepted by the 9th SIGKDD international workshop on
Mining and Learning from Time Series (`MiLeTS'23 <https://kdd-milets.github.io/milets2023/>`_).
Besides, PyPOTS has been included as a `PyTorch Ecosystem <https://landscape.pytorch.org/?item=modeling--specialized--pypots>`_ project.

PyPOTS paper is available on arXiv at `this URL <https://arxiv.org/abs/2305.18811>`_.,
and we are pursuing to publish it in prestigious academic venues, e.g. JMLR (track for
`Machine Learning Open Source Software <https://www.jmlr.org/mloss/>`_). If you use PyPOTS in your work,
please cite it as below and 🌟star `PyPOTS repository <https://github.com/WenjieDu/PyPOTS>`_ to make others notice this library. 🤗

.. code-block:: bibtex
   :linenos:

    @article{du2023pypots,
    title={{PyPOTS: a Python toolbox for data mining on Partially-Observed Time Series}},
    author={Wenjie Du},
    journal={arXiv preprint arXiv:2305.18811},
    year={2023},
    }

or

   Wenjie Du. (2023).
   PyPOTS: A Python Toolbox for Data Mining on Partially-Observed Time Series.
   arXiv, abs/2305.18811. https://doi.org/10.48550/arXiv.2305.18811


Research Projects Using PyPOTS
""""""""""""""""""""""""""""""
There are scientific research projects using PyPOTS and referencing in their papers.
Here is `an incomplete list of them <https://scholar.google.com/scholar?as_ylo=2022&q=%E2%80%9CPyPOTS%E2%80%9D&hl=en>`_.


Project Milestones
^^^^^^^^^^^^^^^^^^
- 2022-03: `PyPOTS project <https://github.com/WenjieDu/PyPOTS>`_ is initiated;
- 2022-04: PyPOTS v0.0.1 is released;
- 2022-09: PyPOTS achieves its first 100 stars ⭐️ on GitHub;
- 2023-03: PyPOTS is `published on Conda-Forge <https://anaconda.org/conda-forge/pypots>`_, and users can install it via Anaconda;
- 2023-04: `PyPOTS website <https://pypots.com>`_ is launched, and PyPOTS achieves its first 10K downloads on PyPI;
- 2023-05: PyPOTS v0.1 is released, and `the preprint paper <https://arxiv.org/abs/2305.18811>`_ is published on arXiv;
- 2023-06: A short version of PyPOTS paper is accepted by the 9th SIGKDD International
  Workshop on Mining and Learning from Time Series (`MiLeTS'23 <https://kdd-milets.github.io/milets2023/>`_);
- 2023-07: PyPOTS has been accepted as a `PyTorch Ecosystem <https://landscape.pytorch.org/?item=modeling--specialized--pypots>`_ project;
- 2023-12: PyPOTS achieves its first 500 stars 🌟;
- 2024-02: PyPOTS Research releases its imputation survey paper `Deep Learning for Multivariate Time Series Imputation: A Survey <https://arxiv.org/abs/2402.04059>`_;
- 2024-06: PyPOTS Research releases the 1st comprehensive time-series imputation benchmark paper `TSI-Bench: Benchmarking Time Series Imputation <https://arxiv.org/abs/2406.12747>`_;
- 2024-07: PyPOTS achieves its first 300,000 downloads in total;
- 2024-08: We present the keynote "Learning from Partially Observed Time Series: Towards Reality-Centric AI4TS" `IJCAI'24 AI4TS workshop <https://ai4ts.github.io/ijcai2024>`_;
