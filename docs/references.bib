@article{cao2018BRITS,
title = {{{BRITS}}: {{Bidirectional Recurrent Imputation}} for {{Time Series}}},
author = {<PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>},
year = {2018},
month = may,
journal = {arXiv:1805.10572 [cs, stat]},
eprint = {1805.10572},
eprinttype = {arxiv},
primaryclass = {cs, stat},
url = {http://arxiv.org/abs/1805.10572},
archiveprefix = {arXiv},
keywords = {Computer Science - Machine Learning,Statistics - Machine Learning}
}

@ARTICLE{yoon2019MRNN,
author={<PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON>},
journal={IEEE Transactions on Biomedical Engineering},
title={Estimating Missing Data in Temporal Data Streams Using Multi-Directional Recurrent Neural Networks},
year={2019},
volume={66},
number={5},
pages={1477-1490},
doi={10.1109/TBME.2018.2874712}
}

@article{che2018GRUD,
title = {Recurrent {{Neural Networks}} for {{Multivariate Time Series}} with {{Missing Values}}},
author = {<PERSON><PERSON>, <PERSON> and <PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>yunghyun and Sontag, David and <PERSON>, <PERSON>},
year = {2018},
month = apr,
journal = {Scientific Reports},
volume = {8},
number = {1},
pages = {6085},
publisher = {{Nature Publishing Group}},
issn = {2045-2322},
doi = {10.1038/s41598-018-24271-9},
url = {https://www.nature.com/articles/s41598-018-24271-9},
copyright = {2018 The Author(s)}
}

@article{chen2021BTMF,
title = {Bayesian {{Temporal Factorization}} for {{Multidimensional Time Series Prediction}}},
author = {Chen, Xinyu and Sun, Lijun},
year = {2021},
journal = {IEEE Transactions on Pattern Analysis and Machine Intelligence},
eprint = {1910.06366},
eprinttype = {arxiv},
pages = {1--1},
issn = {0162-8828, 2160-9292, 1939-3539},
doi = {10.1109/TPAMI.2021.3066551},
url = {http://arxiv.org/abs/1910.06366},
archiveprefix = {arXiv},
keywords = {Computer Science - Machine Learning,Statistics - Machine Learning}
}

@article{choi2020RDISRandom,
title = {{{RDIS}}: {{Random Drop Imputation}} with {{Self-Training}} for {{Incomplete Time Series Data}}},
author = {Choi, Tae-Min and Kang, Ji-Su and Kim, Jong-Hwan},
year = {2020},
month = oct,
journal = {arXiv:2010.10075 [cs, stat]},
eprint = {2010.10075},
eprinttype = {arxiv},
primaryclass = {cs, stat},
url = {http://arxiv.org/abs/2010.10075},
archiveprefix = {arXiv},
keywords = {Computer Science - Machine Learning,Statistics - Machine Learning}
}

@article{cini2021MultivariateTime,
title = {Multivariate {{Time Series Imputation}} by {{Graph Neural Networks}}},
author = {Cini, Andrea and Marisca, Ivan and Alippi, Cesare},
year = {2021},
month = sep,
journal = {arXiv:2108.00298 [cs]},
eprint = {2108.00298},
eprinttype = {arxiv},
primaryclass = {cs},
url = {http://arxiv.org/abs/2108.00298},
archiveprefix = {arXiv},
keywords = {Computer Science - Artificial Intelligence,Computer Science - Machine Learning}
}

@inproceedings{costa2018MissingData,
title = {Missing {{Data Imputation}} via {{Denoising Autoencoders}}: {{The Untold Story}}},
booktitle = {Advances in {{Intelligent Data Analysis XVII}}},
author = {Costa, Adriana Fonseca and Santos, Miriam Seoane and Soares, Jastin Pompeu and Abreu, Pedro Henriques},
editor = {Duivesteijn, Wouter and Siebes, Arno and Ukkonen, Antti},
year = {2018},
series = {Lecture {{Notes}} in {{Computer Science}}},
pages = {87--98},
publisher = {{Springer International Publishing}},
address = {{Cham}},
doi = {10.1007/978-3-030-01768-2_8},
isbn = {978-3-030-01768-2},
keywords = {Data imputation,Denoising autoencoders,Missing data,Missing mechanisms}
}

@article{dejong2019VaDER,
title = {Deep Learning for Clustering of Multivariate Clinical Patient Trajectories with Missing Values},
author = {{de~Jong}, Johann and Emon, Mohammad Asif and Wu, Ping and Karki, Reagon and Sood, Meemansa and Godard, Patrice and Ahmad, Ashar and Vrooman, Henri and {Hofmann-Apitius}, Martin and Fr{\"o}hlich, Holger},
year = {2019},
month = nov,
journal = {GigaScience},
volume = {8},
number = {11},
pages = {giz134},
issn = {2047-217X},
doi = {10.1093/gigascience/giz134},
url = {https://doi.org/10.1093/gigascience/giz134}
}

@article{du2023SAITS,
title = {{SAITS: Self-Attention-based Imputation for Time Series}},
journal = {Expert Systems with Applications},
volume = {219},
pages = {119619},
year = {2023},
issn = {0957-4174},
doi = {10.1016/j.eswa.2023.119619},
url = {https://arxiv.org/abs/2202.08516},
author = {Wenjie Du and David Cote and Yan Liu},
}

@inproceedings{fortuin2020gpvae,
title={{GP-VAE: Deep probabilistic time series imputation}},
author={Fortuin, Vincent and Baranchuk, Dmitry and R{\"a}tsch, Gunnar and Mandt, Stephan},
booktitle={International conference on artificial intelligence and statistics},
pages={1651--1661},
year={2020},
organization={PMLR}
}

@article{horn2019SeFT,
title = {Set {{Functions}} for {{Time Series}}},
author = {Horn, Max and Moor, Michael and Bock, Christian and Rieck, Bastian and Borgwardt, Karsten},
year = {2019},
month = sep,
url = {https://arxiv.org/abs/1909.12064v3}
}

@article{hubert1985AdjustedRI,
title = {Comparing Partitions},
author = {Hubert, Lawrence and Arabie, Phipps},
year = {1985},
month = dec,
journal = {Journal of Classification},
volume = {2},
number = {1},
pages = {193--218},
issn = {1432-1343},
doi = {10.1007/BF01908075},
url = {https://doi.org/10.1007/BF01908075},
keywords = {Consensus indices,Measures of agreement,Measures of association}
}

@article{little1988TestMCAR,
title = {A {{Test}} of {{Missing Completely}} at {{Random}} for {{Multivariate Data}} with {{Missing Values}}},
author = {Little, Roderick J. A.},
year = {1988},
journal = {Journal of the American Statistical Association},
volume = {83},
number = {404},
pages = {1198--1202},
publisher = {{[American Statistical Association, Taylor \& Francis, Ltd.]}},
issn = {0162-1459},
doi = {10.2307/2290157},
url = {https://www.jstor.org/stable/2290157}
}

@inproceedings{liu2019NAOMI,
title = {{{NAOMI}}: {{Non-Autoregressive Multiresolution Sequence Imputation}}},
booktitle = {{{arXiv}}:1901.10946 [Cs, Stat]},
author = {Liu, Yukai and Yu, Rose and Zheng, Stephan and Zhan, Eric and Yue, Yisong},
year = {2019},
month = oct,
eprint = {1901.10946},
eprinttype = {arxiv},
primaryclass = {cs, stat},
url = {http://arxiv.org/abs/1901.10946},
archiveprefix = {arXiv},
keywords = {Computer Science - Machine Learning,Statistics - Machine Learning}
}

@incollection{luo2018MultivariateTime,
title = {Multivariate {{Time Series Imputation}} with {{Generative Adversarial Networks}}},
booktitle = {Advances in {{Neural Information Processing Systems}} 31},
author = {Luo, Yonghong and Cai, Xiangrui and ZHANG, Ying and Xu, Jun and {xiaojie}, Yuan},
editor = {Bengio, S. and Wallach, H. and Larochelle, H. and Grauman, K. and {Cesa-Bianchi}, N. and Garnett, R.},
year = {2018},
pages = {1596--1607},
publisher = {{Curran Associates, Inc.}},
url = {http://papers.nips.cc/paper/7432-multivariate-time-series-imputation-with-generative-adversarial-networks.pdf}
}

@article{ma2019CDSA,
title = {{{CDSA}}: {{Cross-Dimensional Self-Attention}} for {{Multivariate}}, {{Geo-tagged Time Series Imputation}}},
author = {Ma, Jiawei and Shou, Zheng and Zareian, Alireza and Mansour, Hassan and Vetro, Anthony and Chang, Shih-Fu},
year = {2019},
month = aug,
journal = {arXiv:1905.09904 [cs, stat]},
eprint = {1905.09904},
eprinttype = {arxiv},
primaryclass = {cs, stat},
url = {http://arxiv.org/abs/1905.09904},
archiveprefix = {arXiv},
keywords = {Computer Science - Machine Learning,Statistics - Machine Learning}
}

@article{ma2021CRLI,
title = {Learning {{Representations}} for {{Incomplete Time Series Clustering}}},
author = {Ma, Qianli and Chen, Chuxin and Li, Sen and Cottrell, Garrison W.},
year = {2021},
month = may,
journal = {Proceedings of the AAAI Conference on Artificial Intelligence},
volume = {35},
number = {10},
pages = {8837--8846},
issn = {2374-3468},
url = {https://ojs.aaai.org/index.php/AAAI/article/view/17070},
copyright = {Copyright (c) 2021 Association for the Advancement of Artificial Intelligence},
keywords = {Time-Series/Data Streams}
}

@article{miao2021SSGAN,
title = {Generative {{Semi-supervised Learning}} for {{Multivariate Time Series Imputation}}},
author = {Miao, Xiaoye and Wu, Yangyang and Wang, Jun and Gao, Yunjun and Mao, Xudong and Yin, Jianwei},
year = {2021},
month = may,
journal = {Proceedings of the AAAI Conference on Artificial Intelligence},
volume = {35},
number = {10},
pages = {8983--8991},
issn = {2374-3468},
url = {https://ojs.aaai.org/index.php/AAAI/article/view/17086},
copyright = {Copyright (c) 2021 Association for the Advancement of Artificial Intelligence},
keywords = {Time-Series/Data Streams}
}

@article{mikalsen2017TimeSeries,
title = {Time {{Series Cluster Kernel}} for {{Learning Similarities}} between {{Multivariate Time Series}} with {{Missing Data}}},
author = {Mikalsen, Karl {\O}yvind and Bianchi, Filippo Maria and {Soguero-Ruiz}, Cristina and Jenssen, Robert},
year = {2017},
month = jun,
journal = {arXiv:1704.00794 [cs, stat]},
eprint = {1704.00794},
eprinttype = {arxiv},
primaryclass = {cs, stat},
url = {http://arxiv.org/abs/1704.00794},
archiveprefix = {arXiv},
keywords = {Computer Science - Machine Learning,Statistics - Machine Learning}
}

@inproceedings{oh2021STINGSelfattention,
title = {{{STING}}: {{Self-attention}} Based {{Time-series Imputation Networks}} Using {{GAN}}},
booktitle = {2021 {{IEEE International Conference}} on {{Data Mining}} ({{ICDM}})},
author = {Oh, Eunkyu and Kim, Taehun and Ji, Yunhu and Khyalia, Sushil},
year = {2021},
month = dec,
pages = {1264--1269},
issn = {2374-8486},
doi = {10.1109/ICDM51629.2021.00155},
keywords = {bidirectional RNN,Conferences,Correlation,Data collection,Deep learning,generative adversarial networks,Generative adversarial networks,Recurrent neural networks,self-attention,Time series analysis,time-series imputation}
}

@article{oyvindmikalsen2021TimeSeries,
title = {Time Series Cluster Kernels to Exploit Informative Missingness and Incomplete Label Information},
author = {{\O}yvind Mikalsen, Karl and {Soguero-Ruiz}, Cristina and Maria Bianchi, Filippo and Revhaug, Arthur and Jenssen, Robert},
year = {2021},
month = jul,
journal = {Pattern Recognition},
volume = {115},
pages = {107896},
issn = {0031-3203},
doi = {10.1016/j.patcog.2021.107896},
url = {https://www.sciencedirect.com/science/article/pii/S0031320321000832},
keywords = {Informative missingness,Kernel methods,Missing data,Multivariate time series,Semi-supervised learning}
}

@article{rand1971RandIndex,
title = {Objective {{Criteria}} for the {{Evaluation}} of {{Clustering Methods}}},
author = {Rand, William M.},
year = {1971},
journal = {Journal of the American Statistical Association},
volume = {66},
number = {336},
pages = {846--850},
publisher = {{[American Statistical Association, Taylor \& Francis, Ltd.]}},
issn = {0162-1459},
doi = {10.2307/2284239},
url = {https://www.jstor.org/stable/2284239}
}

@article{shukla2021MultiTimeAttention,
title = {Multi-{{Time Attention Networks}} for {{Irregularly Sampled Time Series}}},
author = {Shukla, Satya Narayan and Marlin, Benjamin M.},
year = {2021},
month = jun,
journal = {arXiv:2101.10318 [cs]},
eprint = {2101.10318},
eprinttype = {arxiv},
primaryclass = {cs},
url = {http://arxiv.org/abs/2101.10318},
archiveprefix = {arXiv},
keywords = {Computer Science - Artificial Intelligence,Computer Science - Machine Learning}
}

@inproceedings{suo2020GLIMAGlobal,
title = {{{GLIMA}}: {{Global}} and {{Local Time Series Imputation}} with {{Multi-directional Attention Learning}}},
booktitle = {2020 {{IEEE International Conference}} on {{Big Data}} ({{Big Data}})},
author = {Suo, Qiuling and Zhong, Weida and Xun, Guangxu and Sun, Jianhui and Chen, Changyou and Zhang, Aidong},
year = {2020},
month = dec,
pages = {798--807},
doi = {10.1109/BigData50022.2020.9378408},
keywords = {Big Data,Conferences,Correlation,Missing Data,Recurrent Imputation,Recurrent neural networks,Self-Attention,Task analysis,Tensors,Time Series,Time series analysis}
}

@article{tang2019JointModeling,
title = {Joint {{Modeling}} of {{Local}} and {{Global Temporal Dynamics}} for {{Multivariate Time Series Forecasting}} with {{Missing Values}}},
author = {Tang, Xianfeng and Yao, Huaxiu and Sun, Yiwei and Aggarwal, Charu and Mitra, Prasenjit and Wang, Suhang},
year = {2019},
month = nov,
journal = {arXiv:1911.10273 [cs, stat]},
eprint = {1911.10273},
eprinttype = {arxiv},
primaryclass = {cs, stat},
url = {http://arxiv.org/abs/1911.10273},
archiveprefix = {arXiv},
keywords = {Computer Science - Machine Learning,Statistics - Machine Learning}
}

@inproceedings{vaswani2017Transformer,
author = {Vaswani, Ashish and Shazeer, Noam and Parmar, Niki and Uszkoreit, Jakob and Jones, Llion and Gomez, Aidan N and Kaiser, \L ukasz and Polosukhin, Illia},
booktitle = {Advances in Neural Information Processing Systems},
editor = {I. Guyon and U. Von Luxburg and S. Bengio and H. Wallach and R. Fergus and S. Vishwanathan and R. Garnett},
pages = {},
publisher = {Curran Associates, Inc.},
title = {Attention is All you Need},
url = {https://proceedings.neurips.cc/paper/2017/file/3f5ee243547dee91fbd053c1c4a845aa-Paper.pdf},
volume = {30},
year = {2017}
}

@article{wu2015TimeSeries,
title = {Time {{Series Forecasting}} with {{Missing Values}}},
author = {Wu, Shin-Fu and Chang, Chia-Yung and Lee, Shie-Jue},
year = {2015},
month = apr,
journal = {EAI Endorsed Transactions on Cognitive Communications},
volume = {"1"},
number = {4},
issn = {2313-4534},
url = {https://eudl.eu/doi/10.4108/icst.iniscom.2015.258269}
}

@article{yuan2019E2GAN,
title = {{{E}}{$^{2}$}{{GAN}}: {{End-to-End Generative Adversarial Network}} for {{Multivariate Time Series Imputation}}},
author = {Yuan, Xiaojie and Luo, Yonghong and Zhang, Ying and Cai, Xiangrui},
year = {2019},
pages = {3094--3100},
url = {https://www.ijcai.org/Proceedings/2019/429}
}

@inproceedings{zhang2022Raindrop,
title={Graph-Guided Network for Irregularly Sampled Multivariate Time Series},
author={Xiang Zhang and Marko Zeman and Theodoros Tsiligkaridis and Marinka Zitnik},
booktitle={International Conference on Learning Representations},
year={2022},
url={https://openreview.net/forum?id=Kwm8I7dU-l5}
}

@inproceedings{reddi2018OnTheConvergence,
title={On the Convergence of Adam and Beyond},
author={Sashank J. Reddi and Satyen Kale and Sanjiv Kumar},
booktitle={International Conference on Learning Representations},
year={2018},
url={https://openreview.net/forum?id=ryQu7f-RZ},
}

@article{hubert1985,
title={Comparing partitions},
author={Hubert, Lawrence and Arabie, Phipps},
journal={Journal of classification},
volume={2},
pages={193--218},
year={1985},
publisher={Springer}
}

@article{steinley2004,
title={Properties of the hubert-arable adjusted rand index},
author={Steinley, Douglas},
journal={Psychological methods},
volume={9},
number={3},
pages={386},
year={2004},
publisher={American Psychological Association}
}

@article{calinski1974,
title={A dendrite method for cluster analysis},
author={Cali{\'n}ski, Tadeusz and Harabasz, Jerzy},
journal={Communications in Statistics-theory and Methods},
volume={3},
number={1},
pages={1--27},
year={1974},
publisher={Taylor \& Francis}
}

@inproceedings{tashiro2021csdi,
title={{CSDI}: Conditional Score-based Diffusion Models for Probabilistic Time Series Imputation},
author={YUSUKE TASHIRO and Jiaming Song and Yang Song and Stefano Ermon},
booktitle={Advances in Neural Information Processing Systems},
editor={A. Beygelzimer and Y. Dauphin and P. Liang and J. Wortman Vaughan},
year={2021},
url={https://openreview.net/forum?id=VzuIzbRDrum}
}

@article{rubin1976missing,
ISSN = {00063444},
URL = {http://www.jstor.org/stable/2335739},
author = {Donald B. Rubin},
journal = {Biometrika},
number = {3},
pages = {581--592},
publisher = {[Oxford University Press, Biometrika Trust]},
title = {Inference and Missing Data},
volume = {63},
year = {1976}
}

@inproceedings{ipsen2021notmiwae,
title={not-{\{}MIWAE{\}}: Deep Generative Modelling with Missing not at Random Data},
author={Niels Bruun Ipsen and Pierre-Alexandre Mattei and Jes Frellsen},
booktitle={International Conference on Learning Representations},
year={2021},
url={https://openreview.net/forum?id=tu29GQT0JFy}
}

@inproceedings{wu2023timesnet,
title={{TimesNet: Temporal 2D-Variation Modeling for General Time Series Analysis}},
author={Haixu Wu and Tengge Hu and Yong Liu and Hang Zhou and Jianmin Wang and Mingsheng Long},
booktitle={The Eleventh International Conference on Learning Representations },
year={2023},
url={https://openreview.net/forum?id=ju_Uqw384Oq}
}

@inproceedings{wu2021autoformer,
author = {Wu, Haixu and Xu, Jiehui and Wang, Jianmin and Long, Mingsheng},
booktitle = {Advances in Neural Information Processing Systems},
pages = {22419--22430},
publisher = {Curran Associates, Inc.},
title = {Autoformer: Decomposition Transformers with Auto-Correlation for Long-Term Series Forecasting},
url = {https://proceedings.neurips.cc/paper_files/paper/2021/file/bcc0d400288793e8bdcd7c19a8ac0c2b-Paper.pdf},
volume = {34},
year = {2021}
}

@inproceedings{zhang2023crossformer,
title={Crossformer: Transformer Utilizing Cross-Dimension Dependency for Multivariate Time Series Forecasting},
author={Yunhao Zhang and Junchi Yan},
booktitle={The Eleventh International Conference on Learning Representations},
year={2023},
url={https://openreview.net/forum?id=vSVLM2j9eie}
}

@inproceedings{zeng2023dlinear,
title={Are Transformers Effective for Time Series Forecasting?},
volume={37},
url={https://ojs.aaai.org/index.php/AAAI/article/view/26317},
DOI={10.1609/aaai.v37i9.26317},
number={9},
booktitle={Proceedings of the AAAI Conference on Artificial Intelligence},
author={Zeng, Ailing and Chen, Muxi and Zhang, Lei and Xu, Qiang},
year={2023},
month={Jun.},
pages={11121-11128}
}

@inproceedings{nie2023patchtst,
title={A Time Series is Worth 64 Words:  Long-term Forecasting with Transformers},
author={Yuqi Nie and Nam H Nguyen and Phanwadee Sinthong and Jayant Kalagnanam},
booktitle={The Eleventh International Conference on Learning Representations },
year={2023},
url={https://openreview.net/forum?id=Jbdc0vTOcol}
}

@inproceedings{woo2023etsformer,
title={{ETS}former: Exponential Smoothing Transformers for Time-series Forecasting},
author={Gerald Woo and Chenghao Liu and Doyen Sahoo and Akshat Kumar and Steven Hoi},
booktitle={The Eleventh International Conference on Learning Representations},
year={2023},
url={https://openreview.net/forum?id=5m_3whfo483}
}

@inproceedings{zhou2022fedformer,
title = {{FED}former: Frequency Enhanced Decomposed Transformer for Long-term Series Forecasting},
author = {Zhou, Tian and Ma, Ziqing and Wen, Qingsong and Wang, Xue and Sun, Liang and Jin, Rong},
booktitle = {Proceedings of the 39th International Conference on Machine Learning},
pages = {27268--27286},
year = {2022},
editor = {Chaudhuri, Kamalika and Jegelka, Stefanie and Song, Le and Szepesvari, Csaba and Niu, Gang and Sabato, Sivan},
volume = {162},
series = {Proceedings of Machine Learning Research},
month = {17--23 Jul},
publisher = {PMLR},
pdf = {https://proceedings.mlr.press/v162/zhou22g/zhou22g.pdf},
url = {https://proceedings.mlr.press/v162/zhou22g.html},
}

@inproceedings{zhou2021informer,
title={Informer: Beyond efficient transformer for long sequence time-series forecasting},
author={Zhou, Haoyi and Zhang, Shanghang and Peng, Jieqi and Zhang, Shuai and Li, Jianxin and Xiong, Hui and Zhang, Wancai},
booktitle={Proceedings of the AAAI conference on artificial intelligence},
volume={35},
number={12},
pages={11106--11115},
year={2021}
}

@inproceedings{zhou2022film,
author = {Zhou, Tian and MA, Ziqing and wang, xue and Wen, Qingsong and Sun, Liang and Yao, Tao and Yin, Wotao and Jin, Rong},
booktitle = {Advances in Neural Information Processing Systems},
editor = {S. Koyejo and S. Mohamed and A. Agarwal and D. Belgrave and K. Cho and A. Oh},
pages = {12677--12690},
publisher = {Curran Associates, Inc.},
title = {{FiLM: Frequency improved Legendre Memory Model for Long-term Time Series Forecasting}},
url = {https://proceedings.neurips.cc/paper_files/paper/2022/file/524ef58c2bd075775861234266e5e020-Paper-Conference.pdf},
volume = {35},
year = {2022}
}

@inproceedings{yi2023frets,
author = {Yi, Kun and Zhang, Qi and Fan, Wei and Wang, Shoujin and Wang, Pengyang and He, Hui and An, Ning and Lian, Defu and Cao, Longbing and Niu, Zhendong},
booktitle = {Advances in Neural Information Processing Systems},
editor = {A. Oh and T. Neumann and A. Globerson and K. Saenko and M. Hardt and S. Levine},
pages = {76656--76679},
publisher = {Curran Associates, Inc.},
title = {Frequency-domain MLPs are More Effective Learners in Time Series Forecasting},
url = {https://proceedings.neurips.cc/paper_files/paper/2023/file/f1d16af76939f476b5f040fd1398c0a3-Paper-Conference.pdf},
volume = {36},
year = {2023}
}

@inproceedings{liu2024itransformer,
title={{iTransformer: Inverted Transformers Are Effective for Time Series Forecasting}},
author={Yong Liu and Tengge Hu and Haoran Zhang and Haixu Wu and Shiyu Wang and Lintao Ma and Mingsheng Long},
booktitle={The Twelfth International Conference on Learning Representations},
year={2024},
url={https://openreview.net/forum?id=JePfAI8fah}
}

@inproceedings{liu2022nonstationary,
author = {Liu, Yong and Wu, Haixu and Wang, Jianmin and Long, Mingsheng},
booktitle = {Advances in Neural Information Processing Systems},
editor = {S. Koyejo and S. Mohamed and A. Agarwal and D. Belgrave and K. Cho and A. Oh},
pages = {9881--9893},
publisher = {Curran Associates, Inc.},
title = {Non-stationary Transformers: Exploring the Stationarity in Time Series Forecasting},
url = {https://proceedings.neurips.cc/paper_files/paper/2022/file/4054556fcaa934b0bf76da52cf4f92cb-Paper-Conference.pdf},
volume = {35},
year = {2022}
}

@inproceedings{liu2023koopa,
author = {Liu, Yong and Li, Chenyu and Wang, Jianmin and Long, Mingsheng},
booktitle = {Advances in Neural Information Processing Systems},
editor = {A. Oh and T. Neumann and A. Globerson and K. Saenko and M. Hardt and S. Levine},
pages = {12271--12290},
publisher = {Curran Associates, Inc.},
title = {Koopa: Learning Non-stationary Time Series Dynamics with Koopman Predictors},
url = {https://proceedings.neurips.cc/paper_files/paper/2023/file/28b3dc0970fa4624a63278a4268de997-Paper-Conference.pdf},
volume = {36},
year = {2023}
}

@inproceedings{liu2022pyraformer,
title={Pyraformer: Low-Complexity Pyramidal Attention for Long-Range Time Series Modeling and Forecasting},
author={Shizhan Liu and Hang Yu and Cong Liao and Jianguo Li and Weiyao Lin and Alex X. Liu and Schahram Dustdar},
booktitle={International Conference on Learning Representations},
year={2022},
url={https://openreview.net/forum?id=0EXmFzUn5I}
}

@inproceedings{kitaev2020reformer,
title={Reformer: The Efficient Transformer},
author={Nikita Kitaev and Lukasz Kaiser and Anselm Levskaya},
booktitle={International Conference on Learning Representations},
year={2020},
url={https://openreview.net/forum?id=rkgNKkHtvB}
}

@article{das2023tide,
title={Long-term Forecasting with Ti{DE}: Time-series Dense Encoder},
author={Abhimanyu Das and Weihao Kong and Andrew Leach and Shaan K Mathur and Rajat Sen and Rose Yu},
journal={Transactions on Machine Learning Research},
issn={2835-8856},
year={2023},
url={https://openreview.net/forum?id=pCbC3aQB5W},
}

@inproceedings{chen2023contiformer,
title={ContiFormer: Continuous-Time Transformer for Irregular Time Series Modeling},
author={Yuqi Chen and Kan Ren and Yansen Wang and Yuchen Fang and Weiwei Sun and Dongsheng Li},
booktitle={Thirty-seventh Conference on Neural Information Processing Systems},
year={2023},
url={https://openreview.net/forum?id=YJDz4F2AZu}
}

@inproceedings{lee2024pits,
title={Learning to Embed Time Series Patches Independently},
author={Seunghan Lee and Taeyoung Park and Kibok Lee},
booktitle={The Twelfth International Conference on Learning Representations},
year={2024},
url={https://openreview.net/forum?id=WS7GuBDFa2}
}

@inproceedings{wang2023micn,
title={{MICN}: Multi-scale Local and Global Context Modeling for Long-term Series Forecasting},
author={Huiqiang Wang and Jian Peng and Feihu Huang and Jince Wang and Junhui Chen and Yifei Xiao},
booktitle={The Eleventh International Conference on Learning Representations},
year={2023},
url={https://openreview.net/forum?id=zt53IDUR1U}
}

@inproceedings{wang2024timemixer,
title={TimeMixer: Decomposable Multiscale Mixing for Time Series Forecasting},
author={Shiyu Wang and Haixu Wu and Xiaoming Shi and Tengge Hu and Huakun Luo and Lintao Ma and James Y. Zhang and JUN ZHOU},
booktitle={The Twelfth International Conference on Learning Representations},
year={2024},
url={https://openreview.net/forum?id=7oLshfEIC2}
}

@article{gu2023mamba,
title={Mamba: Linear-Time Sequence Modeling with Selective State Spaces},
author={Gu, Albert and Dao, Tri},
journal={arXiv preprint arXiv:2312.00752},
year={2023}
}

@article{zhang2022lightts,
title={Less Is More: Fast Multivariate Time Series Forecasting with Light Sampling-oriented MLP Structures},
author={Tianping Zhang and Yizhuo Zhang and Wei Cao and Jiang Bian and Xiaohan Yi and Shun Zheng and Jian Li},
year={2022},
eprint={2207.01186},
archivePrefix={arXiv},
primaryClass={cs.LG}
}

@article{lin2023segrnn,
title={{SegRNN}: Segment Recurrent Neural Network for Long-Term Time Series Forecasting},
author={Shengsheng Lin and Weiwei Lin and Wentai Wu and Feiyu Zhao and Ruichao Mo and Haotong Zhang},
year={2023},
eprint={2308.11200},
archivePrefix={arXiv},
primaryClass={cs.LG}
}

@article{chen2023tsmixer,
title={{TSMixer}: An All-MLP Architecture for Time Series Forecasting},
author={Si-An Chen and Chun-Liang Li and Nate Yoder and Sercan O. Arik and Tomas Pfister},
year={2023},
eprint={2303.06053},
archivePrefix={arXiv},
primaryClass={cs.LG}
}

@inproceedings{choi2024timecib,
title={Conditional Information Bottleneck Approach for Time Series Imputation},
author={MinGyu Choi and Changhee Lee},
booktitle={The Twelfth International Conference on Learning Representations},
year={2024},
url={https://openreview.net/forum?id=K1mcPiDdOJ}
}

@article{gao2024units,
title={{UniTS}: Building a Unified Time Series Model},
author={Gao, Shanghua and Koker, Teddy and Queen, Owen and Hartvigsen, Thomas and Tsiligkaridis, Theodoros and Zitnik, Marinka},
journal={arXiv},
url={https://arxiv.org/pdf/2403.00131.pdf},
year={2024}
}

@article{liu2024timesurl,
title={{TimesURL}: Self-Supervised Contrastive Learning for Universal Time Series Representation Learning},
author={Liu, Jiexi and Chen, Songcan},
volume={38},
url={https://ojs.aaai.org/index.php/AAAI/article/view/29299},
DOI={10.1609/aaai.v38i12.29299},
number={12},
journal={Proceedings of the AAAI Conference on Artificial Intelligence},
year={2024},
month={Mar.},
pages={13918-13926},
}

@inproceedings{luo2024moderntcn,
title={Modern{TCN}: A Modern Pure Convolution Structure for General Time Series Analysis},
author={Luo Donghao, Wue Xue},
booktitle={The Twelfth International Conference on Learning Representations},
year={2024},
url={https://openreview.net/forum?id=vpJMJerXHU}
}

@inproceedings{liu2022scinet,
author = {LIU, Minhao and Zeng, Ailing and Chen, Muxi and Xu, Zhijian and LAI, Qiuxia and Ma, Lingna and Xu, Qiang},
booktitle = {Advances in Neural Information Processing Systems},
editor = {S. Koyejo and S. Mohamed and A. Agarwal and D. Belgrave and K. Cho and A. Oh},
pages = {5816--5828},
publisher = {Curran Associates, Inc.},
title = {SCINet: Time Series Modeling and Forecasting with Sample Convolution and Interaction},
url = {https://proceedings.neurips.cc/paper_files/paper/2022/file/266983d0949aed78a16fa4782237dea7-Paper-Conference.pdf},
volume = {35},
year = {2022}
}

@inproceedings{kim2022revin,
title={Reversible Instance Normalization for Accurate Time-Series Forecasting against Distribution Shift},
author={Taesung Kim and Jinhee Kim and Yunwon Tae and Cheonbok Park and Jang-Ho Choi and Jaegul Choo},
booktitle={International Conference on Learning Representations},
year={2022},
url={https://openreview.net/forum?id=cGDAkQo1C0p}
}

@inproceedings{cao2020stemgnn,
author = {Cao, Defu and Wang, Yujing and Duan, Juanyong and Zhang, Ce and Zhu, Xia and Huang, Congrui and Tong, Yunhai and Xu, Bixiong and Bai, Jing and Tong, Jie and Zhang, Qi},
booktitle = {Advances in Neural Information Processing Systems},
editor = {H. Larochelle and M. Ranzato and R. Hadsell and M.F. Balcan and H. Lin},
pages = {17766--17778},
publisher = {Curran Associates, Inc.},
title = {Spectral Temporal Graph Neural Network for Multivariate Time-series Forecasting},
url = {https://proceedings.neurips.cc/paper_files/paper/2020/file/cdf6581cb7aca4b7e19ef136c6e601a5-Paper.pdf},
volume = {33},
year = {2020}
}

@inproceedings{xu2024fits,
title={{FITS}: Modeling Time Series with \$10k\$ Parameters},
author={Zhijian Xu and Ailing Zeng and Qiang Xu},
booktitle={The Twelfth International Conference on Learning Representations},
year={2024},
url={https://openreview.net/forum?id=bWcnvZ3qMb}
}

@inproceedings{nie2024imputeformer,
title={ImputeFormer: Low Rankness-Induced Transformers for Generalizable Spatiotemporal Imputation},
author={Nie, Tong and Qin, Guoyang and Ma, Wei and Mei, Yuewen and Sun, Jian},
booktitle = {Proceedings of the 30th ACM SIGKDD Conference on Knowledge Discovery and Data Mining},
publisher = {Association for Computing Machinery},
year={2024},
series = {KDD '24},
doi = {10.1145/3637528.3671751},
url = {https://doi.org/10.1145/3637528.3671751},
}

@article{bai2018tcn,
title={An empirical evaluation of generic convolutional and recurrent networks for sequence modeling},
author={Bai, Shaojie and Kolter, J Zico and Koltun, Vladlen},
journal={arXiv preprint arXiv:1803.01271},
year={2018}
}

@article{zhan2025tefn,
author={Zhan, Tianxiang and He, Yuanpeng and Deng, Yong and Li, Zhen and Du, Wenjie and Wen, Qingsong},
journal={IEEE Transactions on Pattern Analysis and Machine Intelligence},
title={Time Evidence Fusion Network: Multi-Source View in Long-Term Time Series Forecasting},
year={2025},
volume={},
number={},
pages={1-15},
doi={10.1109/TPAMI.2025.3596905}
}

@inproceedings{jin2024timellm,
title={Time-{LLM}: Time Series Forecasting by Reprogramming Large Language Models},
author={Ming Jin and Shiyu Wang and Lintao Ma and Zhixuan Chu and James Y. Zhang and Xiaoming Shi and Pin-Yu Chen and Yuxuan Liang and Yuan-Fang Li and Shirui Pan and Qingsong Wen},
booktitle={The Twelfth International Conference on Learning Representations},
year={2024},
url={https://openreview.net/forum?id=Unb5CVPtae}
}

@article{qian2023csai,
title={Knowledge Enhanced Conditional Imputation for Healthcare Time-series},
author={Qian, Linglong and Ibrahim, Zina and Ellis, Hugh Logan and Zhang, Ao and Zhang, Yuezhou and Wang, Tao and Dobson, Richard},
journal={arXiv preprint arXiv:2312.16713},
year={2023}
}

@article{yu2016trmf,
title={Temporal regularized matrix factorization for high-dimensional time series prediction},
author={Yu, Hsiang-Fu and Rao, Nikhil and Dhillon, Inderjit S},
journal={Advances in neural information processing systems},
volume={29},
year={2016}
}

@inproceedings{zhou2023gpt4ts,
title={One Fits All: Power General Time Series Analysis by Pretrained {LM}},
author={Tian Zhou and Peisong Niu and Xue Wang and Liang Sun and Rong Jin},
booktitle={Thirty-seventh Conference on Neural Information Processing Systems},
year={2023},
url={https://openreview.net/forum?id=gMS6FVZvmF}
}

@article{yue2022ts2vec,
title={TS2Vec: Towards Universal Representation of Time Series},
volume={36},
url={https://ojs.aaai.org/index.php/AAAI/article/view/20881},
DOI={10.1609/aaai.v36i8.20881},
number={8},
journal={Proceedings of the AAAI Conference on Artificial Intelligence},
author={Yue, Zhihan and Wang, Yujing and Duan, Juanyong and Yang, Tianmeng and Huang, Congrui and Tong, Yunhai and Xu, Bixiong},
year={2022},
month={Jun.},
pages={8980-8987}
}

@InProceedings{goswami2024moment,
title = {{MOMENT}: A Family of Open Time-series Foundation Models},
author = {Goswami, Mononito and Szafer, Konrad and Choudhry, Arjun and Cai, Yifu and Li, Shuo and Dubrawski, Artur},
booktitle = {Proceedings of the 41st International Conference on Machine Learning},
pages = {16115--16152},
year = {2024},
editor = {Salakhutdinov, Ruslan and Kolter, Zico and Heller, Katherine and Weller, Adrian and Oliver, Nuria and Scarlett, Jonathan and Berkenkamp, Felix},
volume = {235},
series = {Proceedings of Machine Learning Research},
month = {21--27 Jul},
publisher =    {PMLR},
pdf = {https://raw.githubusercontent.com/mlresearch/v235/main/assets/goswami24a/goswami24a.pdf},
url = {https://proceedings.mlr.press/v235/goswami24a.html},
}

@inproceedings{wang2025timemixerpp,
title={{TimeMixer++: A General Time Series Pattern Machine for Universal Predictive Analysis}},
author={Shiyu Wang and Jiawei LI and Xiaoming Shi and Zhou Ye and Baichuan Mo and Wenze Lin and Ju Shengtong and Zhixuan Chu and Ming Jin},
booktitle={The Thirteenth International Conference on Learning Representations},
year={2025},
url={https://openreview.net/forum?id=1CLzLXSFNn}
}

@article{talukder2024totem,
title={{TOTEM}: {TO}kenized Time Series {EM}beddings for General Time Series Analysis},
author={Sabera J Talukder and Yisong Yue and Georgia Gkioxari},
journal={Transactions on Machine Learning Research},
issn={2835-8856},
year={2024},
url={https://openreview.net/forum?id=QlTLkH6xRC},
note={}
}

@InProceedings{eldele2024tslanet,
title = {{TSLAN}et: Rethinking Transformers for Time Series Representation Learning},
author = {Eldele, Emadeldeen and Ragab, Mohamed and Chen, Zhenghua and Wu, Min and Li, Xiaoli},
booktitle = {Proceedings of the 41st International Conference on Machine Learning},
pages = {12409--12428},
year = {2024},
editor = {Salakhutdinov, Ruslan and Kolter, Zico and Heller, Katherine and Weller, Adrian and Oliver, Nuria and Scarlett, Jonathan and Berkenkamp, Felix},
volume = {235},
series = {Proceedings of Machine Learning Research},
month = {21--27 Jul},
publisher = {PMLR},
pdf = {https://raw.githubusercontent.com/mlresearch/v235/main/assets/eldele24a/eldele24a.pdf},
url = {https://proceedings.mlr.press/v235/eldele24a.html},
}
