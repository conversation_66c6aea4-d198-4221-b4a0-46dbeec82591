"""
The package of the partially-observed time-series anomaly detectoin model FEDformer.

Refer to the paper
`<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.
FEDformer: Frequency enhanced decomposed transformer for long-term series forecasting.
In ICML, volume 162 of Proceedings of Machine Learning Research, pages 27268–27286. PMLR, 17–23 Jul 2022.
<https://proceedings.mlr.press/v162/zhou22g/zhou22g.pdf>`_

Notes
-----
This implementation is inspired by the official one https://github.com/MAZiqing/FEDformer

"""

# Created by <PERSON><PERSON> <<EMAIL>>
# License: BSD-3-Clause


from .model import FEDformer

__all__ = [
    "FEDformer",
]
