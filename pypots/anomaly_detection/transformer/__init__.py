"""
The package of the partially-observed time-series anomaly detection model Transformer.

Refer to the papers
`<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>,
and <PERSON><PERSON>.
Attention is all you need.
In Advances in Neural Information Processing Systems, volume 30. Curran Associates, Inc., 2017.
<https://proceedings.neurips.cc/paper/2017/file/3f5ee243547dee91fbd053c1c4a845aa-Paper.pdf>`_
and
`<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>.
SAITS: Self-Attention-based Imputation for Time Series.
Expert Systems with Applications, 219:119619, 2023.
<https://arxiv.org/pdf/2202.08516>`_

Notes
-----
This implementation is inspired by https://github.com/WenjieDu/SAITS
"""

# Created by <PERSON><PERSON> <<EMAIL>>
# License: BSD-3-Clause


from .model import Transformer

__all__ = [
    "Transformer",
]
