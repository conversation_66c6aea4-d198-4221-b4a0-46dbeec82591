"""
The package of the partially-observed time-series anomaly detection model TimeMixer.

Refer to the paper
`<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>.
"TimeMixer: Decomposable Multiscale Mixing for Time Series Forecasting".
In ICLR 2024.
<https://openreview.net/pdf?id=7oLshfEIC2>`_

Notes
-----
This implementation is inspired by the official one https://github.com/kwuking/TimeMixer

"""

# Created by <PERSON><PERSON> <<EMAIL>>
# License: BSD-3-Clause


from .model import TimeMixer

__all__ = [
    "TimeMixer",
]
