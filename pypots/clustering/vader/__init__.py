"""
The package of the partially-observed time-series clustering model VaDER.

Refer to the paper
<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>,
<PERSON>, and <PERSON><PERSON><PERSON>.
Deep learning for clustering of multivariate clinical patient trajectories with missing values.
GigaScience, 8(11):giz134, November 2019.
<https://academic.oup.com/gigascience/article-pdf/8/11/giz134/30797160/giz134.pdf>`_

Notes
-----
This implementation is inspired by the official one https://github.com/johanndejong/VaDER

"""

# Created by <PERSON><PERSON><PERSON> <<EMAIL>>
# License: BSD-3-Clause

from .model import VaDER

__all__ = [
    "VaDER",
]
