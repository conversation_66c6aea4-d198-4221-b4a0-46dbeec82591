"""
The package of the partially-observed time-series imputation model TimesNet.

Refer to the paper
`<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>.
TimesNet: Temporal 2D-Variation Modeling for General Time Series Analysis.
In ICLR, 2023.
<https://openreview.net/pdf?id=ju_Uqw384Oq>`_

Notes
-----
This implementation is inspired by the official one https://github.com/thuml/Time-Series-Library

"""

# Created by <PERSON><PERSON><PERSON> <<EMAIL>>
# License: BSD-3-Clause


from .model import TimesNet

__all__ = [
    "TimesNet",
]
