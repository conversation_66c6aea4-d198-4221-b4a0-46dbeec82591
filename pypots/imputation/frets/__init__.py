"""
The package of the partially-observed time-series imputation model FreTS.

Refer to the paper
`<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>.
"Frequency-domain MLPs are More Effective Learners in Time Series Forecasting."
Advances in Neural Information Processing Systems 36 (2024).
<https://proceedings.neurips.cc/paper_files/paper/2023/file/f1d16af76939f476b5f040fd1398c0a3-Paper-Conference.pdf>`_

Notes
-----
This implementation is inspired by the official one https://github.com/aikunyi/FreTS

"""

# Created by <PERSON><PERSON><PERSON> <<EMAIL>>
# License: BSD-3-Clause


from .model import FreTS

__all__ = [
    "FreTS",
]
