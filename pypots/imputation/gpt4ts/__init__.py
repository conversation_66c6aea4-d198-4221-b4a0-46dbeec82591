"""
The package of the partially-observed time-series imputation model GPT4TS.

Refer to the paper
<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>.
One Fits All: Power General Time Series Analysis by Pretrained LM.
NeurIPS 2023.
<https://openreview.net/forum?id=gMS6FVZvmF>`_

Notes
-----
This implementation is inspired by the official one https://github.com/DAMO-DI-ML/NeurIPS2023-One-Fits-All

"""

# Created by <PERSON><PERSON><PERSON> <<EMAIL>>
# License: BSD-3-Clause

from .model import GPT4TS

__all__ = [
    "GPT4TS",
]
