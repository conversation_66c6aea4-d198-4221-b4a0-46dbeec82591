"""
The package of the partially-observed time-series imputation model SegRNN.

Refer to the paper
`<PERSON>, <PERSON>g and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, Haotong.
Segrnn: Segment recurrent neural network for long-term time series forecasting.
arXiv preprint arXiv:2308.11200.
<https://arxiv.org/abs/2308.11200>`_

Notes
-----
This implementation is inspired by the official one https://github.com/lss-1138/SegRNN

"""

# Created by <PERSON><PERSON><PERSON><PERSON>


from .model import SegRNN

__all__ = [
    "SegRNN",
]
