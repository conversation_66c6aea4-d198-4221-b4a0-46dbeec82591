"""
The package of the partially-observed time-series imputation model Pyraformer.

Refer to the paper
<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>.
"Pyraformer: Low-Complexity Pyramidal Attention for Long-Range Time Series Modeling and Forecasting".
International Conference on Learning Representations. 2022.
<https://openreview.net/pdf?id=0EXmFzUn5I>`_

Notes
-----
This implementation is inspired by the official one https://github.com/ant-research/Pyraformer

"""

# Created by <PERSON><PERSON><PERSON> <<EMAIL>>
# License: BSD-3-Clause


from .model import Pyraformer

__all__ = [
    "Pyraformer",
]
