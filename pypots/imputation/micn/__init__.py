"""
The package of the partially-observed time-series imputation model MICN.

Refer to the paper
<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>
"MICN: Multi-scale Local and Global Context Modeling for Long-term Series Forecasting".
In the Eleventh International Conference on Learning Representations, 2023.
<https://openreview.net/pdf?id=zt53IDUR1U>`_

Notes
-----
This implementation is inspired by the official one https://github.com/wanghq21/MICN

"""

# Created by <PERSON><PERSON><PERSON> <<EMAIL>>
# License: BSD-3-Clause


from .model import MICN

__all__ = [
    "MICN",
]
