"""
The package of the partially-observed time-series classification model Raindrop.

Refer to the paper
<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>.
Graph-guided network for irregularly sampled multivariate time series.
In ICLR, 2022.
<https://openreview.net/forum?id=Kwm8I7dU-l5>`_

Notes
-----
This implementation is inspired by the official one the official implementation https://github.com/mims-harvard/Raindrop

"""

# Created by <PERSON><PERSON><PERSON> <<EMAIL>>
# License: BSD-3-Clause

from .model import Raindrop

__all__ = [
    "Raindrop",
]
