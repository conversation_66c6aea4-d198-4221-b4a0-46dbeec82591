"""
The package including the modules of SegRNN.

Refer to the paper
`<PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON>ng.
Segrnn: Segment recurrent neural network for long-term time series forecasting.
arXiv preprint arXiv:2308.11200.
<https://arxiv.org/abs/2308.11200>`_

Notes
-----
This implementation is inspired by the official one https://github.com/lss-1138/SegRNN

"""

# Created by <PERSON><PERSON><PERSON><PERSON>


from .backbone import BackboneSegRNN

__all__ = [
    "BackboneSegRNN",
]
