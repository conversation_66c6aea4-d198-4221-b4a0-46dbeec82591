"""
The package including the modules of TimeMixer++.

Refer to the paper
<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>.
"TimeMixer++: A General Time Series Pattern Machine for Universal Predictive Analysis".
ICLR 2025.
<https://openreview.net/pdf?id=1CLzLXSFNn>`_

Notes
-----
This implementation is inspired by the official one https://anonymous.4open.science/r/TimeMixerPP

"""

# Created by <PERSON><PERSON><PERSON> <<EMAIL>>
# License: BSD-3-Clause

from .backbone import BackboneTimeMixerPP

__all__ = [
    "BackboneTimeMixerPP",
]
