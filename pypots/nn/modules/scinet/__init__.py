"""
The package including the modules of SCINet.

Refer to the paper
`<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> LAI, <PERSON><PERSON>, and <PERSON><PERSON>.
"SCINet: Time Series Modeling and Forecasting with Sample Convolution and Interaction".
In Advances in Neural Information Processing Systems, 2022.
<https://proceedings.neurips.cc/paper_files/paper/2022/file/266983d0949aed78a16fa4782237dea7-Paper-Conference.pdf>`_


Notes
-----
This implementation is inspired by the official one https://github.com/cure-lab/SCINet

"""

# Created by <PERSON><PERSON><PERSON> <<EMAIL>>
# License: BSD-3-Clause


from .backbone import BackboneSCINet

__all__ = [
    "BackboneSCINet",
]
