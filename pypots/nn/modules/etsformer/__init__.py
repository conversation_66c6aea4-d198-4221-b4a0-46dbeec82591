"""
The package including the modules of ETSformer.

Refer to the paper
<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>.
ETSformer: Exponential smoothing transformers for time-series forecasting.
In ICLR, 2023.
<https://openreview.net/pdf?id=5m_3whfo483>`_

Notes
-----
This implementation is inspired by the official one https://github.com/salesforce/ETSformer

"""

# Created by <PERSON><PERSON><PERSON> <<EMAIL>>
# License: BSD-3-Clause


from .autoencoder import ETSformerEncoder, ETSformerDecoder
from .layers import ETSformerEncoderLayer, ETSformerDecoderLayer, Transform

__all__ = [
    "ETSformerEncoder",
    "ETSformerEncoderLayer",
    "ETSformerDecoder",
    "ETSformerDecoderLayer",
    "Transform",
]
