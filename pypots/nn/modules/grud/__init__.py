"""
The package including the modules of GRU-<PERSON>.

Refer to the paper
`<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>.
Recurrent Neural Networks for Multivariate Time Series with Missing Values.
Scientific Reports, 8(1):6085, April 2018.
<https://www.nature.com/articles/s41598-018-24271-9.pdf>`_

Notes
-----
This implementation is inspired by the official one  https://github.com/PeterChe1990/GRU-D

"""

# Created by <PERSON><PERSON><PERSON> <<EMAIL>>
# License: BSD-3-Clause

from .backbone import BackboneGRUD
from .layers import TemporalDecay

__all__ = [
    "BackboneGRUD",
    "TemporalDecay",
]
