"""
The package including the modules of StemGNN.

Refer to the paper
<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>,
<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>.
"Spectral Temporal Graph Neural Network for Multivariate Time-series Forecasting".
In Advances in Neural Information Processing Systems, 2020.
<https://proceedings.neurips.cc/paper_files/paper/2020/file/cdf6581cb7aca4b7e19ef136c6e601a5-Paper.pdf>`_

Notes
-----
This implementation is inspired by the official one https://github.com/microsoft/StemGNN

"""

# Created by <PERSON><PERSON><PERSON> <<EMAIL>>
# License: BSD-3-Clause


from .backbone import BackboneStemGNN

__all__ = [
    "BackboneStemGNN",
]
