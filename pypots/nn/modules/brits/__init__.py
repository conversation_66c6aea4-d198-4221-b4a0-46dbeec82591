"""
The package including the modules of BRITS.

Refer to the paper
<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>.
BRITS: Bidirectional recurrent imputation for time series.
In Advances in Neural Information Processing Systems, volume 31. Curran Associates, Inc., 2018.
<https://papers.nips.cc/paper_files/paper/2018/file/734e6bfcd358e25ac1db0a4241b95651-Paper.pdf>`_

Notes
-----
This implementation is inspired by the official one https://github.com/caow13/BRITS
The bugs in the original implementation are fixed here.

"""

# Created by <PERSON><PERSON><PERSON> <<EMAIL>>
# License: BSD-3-Clause

from .backbone import BackboneRITS, BackboneBRITS
from .layers import FeatureRegression

__all__ = [
    "BackboneRITS",
    "BackboneBRITS",
    "FeatureRegression",
]
