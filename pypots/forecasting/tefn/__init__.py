"""
The implementation of TEFN for the partially-observed time-series forecasting task.

Refer to the paper
`<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>.
Time Evidence Fusion Network: Multi-source View in Long-Term Time Series Forecasting.
In Arxiv, 2024.
<https://arxiv.org/abs/2405.06419>`_

Notes
-----
This implementation is transferred from the official one https://github.com/ztxtech/Time-Evidence-Fusion-Network

"""

# Created by <PERSON><PERSON><PERSON> <<EMAIL>>
# License: BSD-3-Clause


from .model import TEFN

__all__ = [
    "TEFN",
]
