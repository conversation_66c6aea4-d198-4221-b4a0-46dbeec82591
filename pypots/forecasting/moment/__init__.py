"""
The package including the modules of MOMENT.

Refer to the paper
<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>.
"MOMENT: A Family of Open Time-series Foundation Models".
In ICML, 2024.
<https://proceedings.mlr.press/v235/goswami24a.html>`_

Notes
-----
This implementation is inspired by the official one
https://github.com/moment-timeseries-foundation-model/moment-research

"""

# Created by <PERSON><PERSON><PERSON> <<EMAIL>>
# License: BSD-3-Clause


from .model import MOMENT

__all__ = [
    "MOMENT",
]
