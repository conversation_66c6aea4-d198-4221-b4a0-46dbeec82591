# The template for new models to be included

Congrats! You've made it this far!
We really appreciate that you have taken the time to commit your model to PyPOTS!
Once your model gets included in PyPOTS, it will be available to all PyPOTS users.
Your research work will reach out to more people and be more impactful.

This template is created to help you quickly get started with your model's inclusion.
Your model's main body should be implemented in the `model.py` file.
If your model consists of multiple modules, put them in the `modules.py`.
`dataset.py` should contain the Dataset class assembling the input data for your model.

Please follow the instructions below to complete your model's inclusion:

1. Rename this folder `template` to your model's name;
2. Implement your model according to the `TODO` comments, add necessary comments and docstrings,
   write necessary tests and run them on your local machine to ensure everything works well;
3. Delete this README file and all TODO comments;
4. Raise an issue first to request add your new model, then make a PR to commit your code the `dev` branch of PyPOTS;
