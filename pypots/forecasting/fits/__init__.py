"""
The implementation of FITS for the partially-observed time-series forecasting task.

Refer to the paper
`<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>.
FITS: Modeling Time Series with 10k parameters.
In The Twelfth International Conference on Learning Representations, 2024.
<https://openreview.net/pdf?id=bWcnvZ3qMb>`_

Notes
-----
This implementation is inspired by the official one https://github.com/VEWOXIC/FITS

"""

# Created by <PERSON><PERSON><PERSON> <<EMAIL>>
# License: BSD-3-Clause


from .model import FITS

__all__ = [
    "FITS",
]
