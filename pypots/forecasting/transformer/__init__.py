"""
The implementation of Transformer for the partially-observed time-series forecasting task.

Refer to the papers
`<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>,
and <PERSON><PERSON>.
Attention is all you need.
In Advances in Neural Information Processing Systems, volume 30. Curran Associates, Inc., 2017.
<https://proceedings.neurips.cc/paper/2017/file/3f5ee243547dee91fbd053c1c4a845aa-Paper.pdf>`_

Notes
-----
This implementation is inspired by https://github.com/WenjieDu/SAITS
"""

# Created by <PERSON><PERSON><PERSON> <<EMAIL>>
# License: BSD-3-Clause


from .model import Transformer

__all__ = [
    "Transformer",
]
