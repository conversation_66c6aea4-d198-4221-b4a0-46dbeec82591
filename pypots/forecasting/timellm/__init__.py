"""
The implementation of Time-LLM for the partially-observed time-series forecasting task.

Refer to the paper
<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>,
<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>.
Time-LLM: Time Series Forecasting by Reprogramming Large Language Models.
In the 12th International Conference on Learning Representations, 2024.
<https://openreview.net/pdf?id=Unb5CVPtae>`_

Notes
-----
This implementation is inspired by the official one https://github.com/KimMeen/Time-LLM

"""

# Created by <PERSON><PERSON><PERSON> <<EMAIL>>
# License: BSD-3-Clause


from .model import TimeLLM

__all__ = [
    "TimeLLM",
]
